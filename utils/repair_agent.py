import os
import subprocess
import time
import json
from typing import List, Tuple, Optional, Dict, Any
import tempfile
from utils.function_replacer import FunctionReplacer

# Abstract tool base class
class Tool:
    """
    Abstract base class for tools available within the security agent.
    """
    name: str

    def run(self, *args: str) -> str:
        """
        Execute the tool with provided arguments and return combined stdout and stderr.
        """
        raise NotImplementedError

# Concrete tool implementations
class LsTool(Tool):
    name = 'ls'
    def run(self, *args: str) -> str:
        cmd = ' '.join(['ls'] + list(args))
        result = subprocess.run(['docker', 'exec', self.container, 'bash', '-lc', cmd], capture_output=True, text=True)
        return result.stdout + result.stderr

class CatTool(Tool):
    name = 'cat'
    def run(self, *args: str) -> str:
        cmd = ' '.join(['cat'] + list(args))
        result = subprocess.run(['docker', 'exec', self.container, 'bash', '-lc', cmd], capture_output=True, text=True)
        return result.stdout + result.stderr

class GitTool(Tool):
    name = 'git'
    def run(self, *args: str) -> str:
        cmd = ' '.join(['git'] + list(args))
        result = subprocess.run(['docker', 'exec', self.container, 'bash', '-lc', cmd], capture_output=True, text=True)
        return result.stdout + result.stderr

class BanditTool(Tool):
    name = 'bandit'
    def run(self, *args: str) -> str:
        cmd = ' '.join(['bandit'] + list(args))
        result = subprocess.run(['docker', 'exec', self.container, 'bash', '-lc', cmd], capture_output=True, text=True)
        return result.stdout + result.stderr

class PylintTool(Tool):
    name = 'pylint'
    def run(self, *args: str) -> str:
        cmd = ' '.join(['pylint'] + list(args))
        result = subprocess.run(['docker', 'exec', self.container, 'bash', '-lc', cmd], capture_output=True, text=True)
        return result.stdout + result.stderr

class MypyTool(Tool):
    name = 'mypy'
    def run(self, *args: str) -> str:
        cmd = ' '.join(['mypy'] + list(args))
        result = subprocess.run(['docker', 'exec', self.container, 'bash', '-lc', cmd], capture_output=True, text=True)
        return result.stdout + result.stderr

class ProspectorTool(Tool):
    name = 'prospector'
    def run(self, *args: str) -> str:
        cmd = ' '.join(['prospector'] + list(args))
        result = subprocess.run(['docker', 'exec', self.container, 'bash', '-lc', cmd], capture_output=True, text=True)
        return result.stdout + result.stderr

PROMPT_TEMPLATE = (
    "You are a security-fix agent running within a Docker container.\n"
    "Your mission is to locate and fix a software vulnerability by iteratively reasoning and executing only the allowed tools via function calls.\n"
    "Your task is to fix the vulnerability inside functions of a code. You have access to a set of tools that can help you inspect and modify functions. You can use these tools to locate the vulnerable code, apply the minimal patches to the vulnerable functions.\n"
    "\n"
    "Available tools (called via function-calling interface):\n"
    # "  - ls (list files)\n"
    # "  - cat (read file contents)\n"
    # "  - git (inspect or commit repository state)\n"
    # "  - bandit (run Python security scanner)\n"
    # "  - pylint (check code syntax and other possible errors)\n"
    # "  - mypy (check type correctness)\n"
    # "  - prospector (run meta-analysis combining tools)\n"
    "  - get_function_names (get list of function names from the vulnerable code)\n"
    "  - get_function_definition (get the definition of a specific function from the vulnerable code)\n"
    "  - replace_in_function (replace specific strings within a function in the vulnerable code)\n"
    "\n"
    "To modify functions in the vulnerable code, use the function-specific tools:\n"
    "- First use `get_function_names` to see what functions are available\n"
    "- Use `get_function_definition` to examine specific functions\n"
    "- Use `replace_in_function` to make targeted changes within functions\n"
    "Keep changes minimal. After edits, re-run relevant linters or security tools to validate the fix.\n"
    # "After editing a file, always check for errors and vulnerabilities with bandit, pylint, mypy, and/or prospector. If a tool does not detect errors or vulnerabilities, try with another.\n"
    "\n"
    "<INPUT>\n"
    "vuln_path: {vuln_path}\n"
    "cve_description: {cve_description}\n"
    "</INPUT>\n"
    "<GOAL>\n"
    "Based on the provided cve_description, locate the vulnerable functions and code segments, apply the minimal patch to the vulnerable code using the function tools. If you change a function, check if other functions that caller and callee functions are affected.\n"
    "</GOAL>\n"
    "When you believe the vulnerability is fixed and validated, call finalize_repair via function calling."
)

# Main agent class
class RepairAgent:
    """
    A ReAct-based agent for automated vulnerability patching inside a Docker container.

    Attributes:
        vuln_path (str): Absolute host path to the vulnerable code directory.
        cve_description (str): Description of the vulnerability.
        tools_allowed (List[str]): Keys of allowed tools.
        model_choice (str): 'openai' or 'ollama'.
        client_api_key, base_url, model_name: optional config.
        container (str): Docker container name.
        tool_instances (Dict[str, Tool]): Instantiated tool objects.
    """
    
    def __init__(
        self,
        vuln_path: str,
        host_repo_dir: str,
        cve_description: str,
        # tools_allowed: List[str],
        model_choice: str = 'openai',
        client_api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        model_name: Optional[str] = None,
        container_root: str = '/workspace/vuln'
    ):
        self.vuln_path = vuln_path
        self.host_repo_dir = host_repo_dir
        self.cve_description = cve_description
        # self.tools_allowed = tools_allowed
        self.model_choice = model_choice
        self.client_api_key = client_api_key
        self.base_url = base_url
        self.model_name = model_name
        self.container = f"agent_env_{int(time.time())}"
        self.num_iterations = None
        self.tool_usage = None
        self.container_root = container_root
        self.modified_functions = set()

        # Initialize FunctionReplacer for code analysis and modification
        self.function_replacer = FunctionReplacer()

        # instantiate allowed tools
        self.tool_instances: Dict[str, Tool] = {}
        # for key in tools_allowed:
        #     if key == 'ls': self.tool_instances['ls'] = LsTool()
        #     if key == 'cat': self.tool_instances['cat'] = CatTool()
        #     if key == 'git': self.tool_instances['git'] = GitTool()
        #     if key == 'bandit': self.tool_instances['bandit'] = BanditTool()
        #     if key == 'pylint': self.tool_instances['pylint'] = PylintTool()
        #     if key == 'mypy': self.tool_instances['mypy'] = MypyTool()
        #     if key == 'prospector': self.tool_instances['prospector'] = ProspectorTool()
        # bind container to each tool
        for t in self.tool_instances.values(): setattr(t, 'container', self.container)
        if model_choice == 'openai':
            from openai import OpenAI

            if not client_api_key or not base_url or not model_name:
                raise ValueError("gemini requires client_api_key, base_url and model_name")

            # instantiate the OpenAI-compatible Gemini client
            self.client = OpenAI(
                api_key=client_api_key,
                base_url=base_url
            )
        else:
            raise ValueError("model_choice must be 'openai'")

    def _tools_schema(self) -> List[Dict[str, Any]]:
        common_args_schema = {
            "type": "object",
            "properties": {
                "args": {
                    "type": "array",
                    "items": {"type": "string"},
                    "default": []
                }
            }
        }

        return [
            # Expose each command as its own function-call tool
            # {
            #     "type": "function",
            #     "function": {
            #         "name": "ls",
            #         "description": "List files inside the container. Example: args=['-la','/workspace/vuln']",
            #         "parameters": common_args_schema
            #     }
            # },
            # {
            #     "type": "function",
            #     "function": {
            #         "name": "cat",
            #         "description": "Read file contents inside the container. Example: args=['/workspace/vuln/src/app.py']",
            #         "parameters": common_args_schema
            #     }
            # },
            # {
            #     "type": "function",
            #     "function": {
            #         "name": "git",
            #         "description": "Run git inside the container. Example: args=['-C','/workspace/vuln','status']",
            #         "parameters": common_args_schema
            #     }
            # },
            # {
            #     "type": "function",
            #     "function": {
            #         "name": "bandit",
            #         "description": "Run Bandit security scanner. Example: args=['-r','/workspace/vuln','-f','json','--quiet']",
            #         "parameters": common_args_schema
            #     }
            # },
            # {
            #     "type": "function",
            #     "function": {
            #         "name": "pylint",
            #         "description": "Run pylint inside the container.",
            #         "parameters": common_args_schema
            #     }
            # },
            # {
            #     "type": "function",
            #     "function": {
            #         "name": "mypy",
            #         "description": "Run mypy type checker inside the container.",
            #         "parameters": common_args_schema
            #     }
            # },
            # {
            #     "type": "function",
            #     "function": {
            #         "name": "prospector",
            #         "description": "Run Prospector meta-analysis inside the container.",
            #         "parameters": common_args_schema
            #     }
            # },

            # Function-specific tools for code analysis and modification
            {
                "type": "function",
                "function": {
                    "name": "get_function_names",
                    "description": "Get a list of all function names defined in the vulnerable code file.",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "get_function_definition",
                    "description": "Get the complete definition of a specific function from the vulnerable code file.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "function_name": {
                                "type": "string",
                                "description": "The name of the function to retrieve."
                            }
                        },
                        "required": ["function_name"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "replace_in_function",
                    "description": "Replace specific strings within a function definition in the vulnerable code file.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "function_name": {
                                "type": "string",
                                "description": "The name of the function to modify."
                            },
                            "replacements": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "old": {"type": "string"},
                                        "new": {"type": "string"}
                                    },
                                    "required": ["old", "new"]
                                },
                                "description": "List of string replacements to apply within the function."
                            }
                        },
                        "required": ["function_name", "replacements"]
                    }
                }
            },

            # Existing finalizer
            {
                "type": "function",
                "function": {
                    "name": "finalize_repair",
                    "description": (
                        "Call this when you believe the vulnerability is fixed and validated. "
                        "This will produce a patch from the staged changes and copy it to the host."
                    ),
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "is_vulnerability_fixed": {
                                "type": "boolean",
                                "description": "Whether the vulnerability has been fixed."
                            }
                        },
                        "required": ["is_vulnerability_fixed"]
                    }
                }
            }

            # Backward-compatibility (optional, can be removed later)
            # {
            #     "type": "function",
            #     "function": {
            #         "name": "run_tool",
            #         "description": (
            #             "(Legacy) Run a whitelisted command (one of: ls, cat, git, bandit, pylint, mypy, prospector) "
            #             "inside the container. Provide each argument as a separate array item."
            #         ),
            #         "parameters": {
            #             "type": "object",
            #             "properties": {
            #                 "tool": {
            #                     "type": "string",
            #                     "enum": ["ls", "cat", "git", "bandit", "pylint", "mypy", "prospector"]
            #                 },
            #                 "args": {
            #                     "type": "array",
            #                     "items": {"type": "string"},
            #                     "default": []
            #                 }
            #             },
            #             "required": ["tool"]
            #         }
            #     }
            # }
        ]

    def _build_prompt(self) -> str:
        return PROMPT_TEMPLATE.format(
            vuln_path=self.vuln_path,
            cve_description=self.cve_description,
        )

    def call_model(self, messages: List[dict]) -> dict:
        if self.model_choice == 'openai':
            resp = self.client.chat.completions.create(
                model=self.model_name,
                messages=messages
            )
            # return resp.choices[0].message
            return {'role': 'assistant', 'content': resp.choices[0].message.content}

        raise ValueError("model_choice not supported.")


    def setup_environment(self):
        subprocess.run(['docker','run','-dit','--name',self.container,'python:3.11-slim'], check=True)
        subprocess.run(['docker', 'exec', self.container, 'mkdir', '-p', self.container_root], check=True)
        cmds=['apt-get update && apt-get install -y git']
        # for key in self.tools_allowed:
        #     if key in ['bandit','pylint','mypy','prospector']: cmds.append(f'pip install {key}')
        for c in cmds:
            subprocess.run(['docker','exec',self.container,'bash','-lc',c], check=True)
        # subprocess.run(['docker','cp',os.path.join(self.host_repo_dir, self.vuln_path),f'{self.container}:/workspace/vuln/'], check=True)
        # Recreate the vuln_path's directory structure inside the container
        rel_path = os.path.normpath(self.vuln_path)
        parent_dir = os.path.dirname(rel_path)
        if parent_dir:
            docker_target_dir = f'{self.container_root}/{parent_dir}'
            subprocess.run(['docker', 'exec', self.container, 'mkdir', '-p', docker_target_dir], check=True)

        # Copy the file or directory to the correct location
        host_full_path = os.path.join(self.host_repo_dir, self.vuln_path)
        container_target_path = f'{self.container}:{self.container_root}/{self.vuln_path}'
        subprocess.run(['docker', 'cp', host_full_path, container_target_path], check=True)

        # Initialize repo
        init_cmds = [
            f'git -C {self.container_root} init',
            f'git -C {self.container_root} config user.email <EMAIL>',
            f'git -C {self.container_root} config user.name Agent',
            f'git -C {self.container_root} add -A',
            f'git -C {self.container_root} commit -m "baseline"',
        ]
        for c in init_cmds:
            subprocess.run(['docker','exec', self.container, 'bash', '-lc', c], check=True)
    
    def execute_tool(self, tool: str, args: Optional[List[str]] = None) -> str:
        args = args or []
        if tool not in self.tool_instances:
            return f"Error: tool '{tool}' not allowed."
        out = self.tool_instances[tool].run(*args)
        return out if out.strip() else "(no output)"

    def _handle_run_tool(self, arguments: Dict[str, Any]) -> str:
        tool = arguments.get('tool')
        args = arguments.get('args') or []
        # Safety: ensure only allowed tools are executed
        # if tool not in self.tools_allowed:
        #     return f"Error: tool '{tool}' not in tools_allowed."
        return self.execute_tool(tool, args)

    def _get_vuln_code_content(self) -> str:
        """Helper method to get the content of the vulnerable code file."""
        try:
            # Get the file content from the container
            result = subprocess.run(
                ["docker", "exec", self.container, "cat", f"{self.container_root}/{self.vuln_path}"],
                capture_output=True, text=True, check=True
            )
            return result.stdout
        except subprocess.CalledProcessError as e:
            return f"Error reading vulnerable code file: {e}"

    def _write_vuln_code_content(self, content: str) -> str:
        """Helper method to write content back to the vulnerable code file."""
        try:
            # Write content to a temporary file and copy it to the container
            tmpdir = tempfile.mkdtemp(prefix="vuln_code_")
            temp_filepath = os.path.join(tmpdir, os.path.basename(self.vuln_path))

            with open(temp_filepath, "w", encoding="utf-8") as f:
                f.write(content)

            subprocess.run(
                ["docker", "cp", temp_filepath, f"{self.container}:{self.container_root}/{self.vuln_path}"],
                check=True
            )

            # Stage the changes
            subprocess.run(
                ["docker", "exec", self.container, "bash", "-lc", f"git -C {self.container_root} add -A"],
                check=True
            )

            return "File updated successfully."
        except Exception as e:
            return f"Error writing vulnerable code file: {e}"

    def _handle_get_function_names(self) -> str:
        """Get list of function names from the vulnerable code."""
        try:
            code_content = self._get_vuln_code_content()
            if code_content.startswith("Error"):
                return code_content

            function_names = self.function_replacer.get_function_names(code_content)
            if not function_names:
                return "No functions found in the vulnerable code file."

            return f"Functions found: {', '.join(function_names)}"
        except Exception as e:
            return f"Error getting function names: {e}"

    def _handle_get_function_definition(self, function_name: str) -> str:
        """Get the definition of a specific function from the vulnerable code."""
        try:
            code_content = self._get_vuln_code_content()
            if code_content.startswith("Error"):
                return code_content

            function_def = self.function_replacer.get_function_definition(code_content, function_name)
            return f"Function definition for '{function_name}':\n\n{function_def}"
        except ValueError as e:
            return f"Function '{function_name}' not found: {e}"
        except Exception as e:
            return f"Error getting function definition: {e}"

    def _handle_replace_in_function(self, function_name: str, replacements: List[Dict[str, str]]) -> str:
        """Replace specific strings within a function in the vulnerable code."""
        try:
            code_content = self._get_vuln_code_content()
            if code_content.startswith("Error"):
                return code_content

            updated_code = self.function_replacer.replace_in_function(code_content, function_name, replacements)

            write_result = self._write_vuln_code_content(updated_code)
            if write_result.startswith("Error"):
                return write_result

            self.modified_functions.add(function_name)

            # Create summary of replacements
            summary = [f"Replacements applied to function '{function_name}':"]
            for i, replacement in enumerate(replacements, 1):
                old = replacement.get('old', '')
                new = replacement.get('new', '')
                summary.append(f"[{i}] '{old}' -> '{new}'")

            return "\n".join(summary) + f"\n\n{write_result}"
        except ValueError as e:
            return f"Function '{function_name}' not found: {e}"
        except Exception as e:
            return f"Error replacing in function: {e}"
    

    def _handle_replace_snippets(self, path: str, replacements: List[Dict[str, str]], stage: bool = True) -> str:
        """
        Replace literal code snippets within a single file (relative to /workspace/vuln).
        Each replacement item must have 'old' and 'new' strings. Replacements are applied sequentially.
        """
        norm = os.path.normpath(path).lstrip("/")
        if norm.startswith("..") or norm == "":
            return "Error: invalid path; it must be inside the repository."

        # Ensure the target file exists
        exists = subprocess.run(
            ["docker", "exec", self.container, "bash", "-lc", f"test -f {self.container_root}/{norm} && echo OK || echo MISSING"],
            capture_output=True, text=True
        )
        if "MISSING" in (exists.stdout or ""):
            return f"Error: file '{norm}' does not exist."

        # Copy the file out, modify on host, copy back
        tmpdir = tempfile.mkdtemp(prefix="replace_snippets_")
        temp_filepath = os.path.join(tmpdir, os.path.basename(norm))
        subprocess.run(["docker", "cp", f"{self.container}:{self.container_root}/{norm}", temp_filepath], check=True)

        with open(temp_filepath, "r", encoding="utf-8", errors="replace") as f:
            content = f.read()

        summary = []
        for i, pair in enumerate(replacements, 1):
            old = pair.get("old")
            new = pair.get("new")
            if old is None or new is None:
                summary.append(f"[{i}] Skipped: missing 'old' or 'new'.")
                continue
            count = content.count(old)
            if count == 0:
                summary.append(f"[{i}] Not found.")
            else:
                content = content.replace(old, new)
                summary.append(f"[{i}] Replaced {count} occurrence(s).")

        with open(temp_filepath, "w", encoding="utf-8") as f:
            f.write(content)

        subprocess.run(["docker", "cp", temp_filepath, f"{self.container}:{self.container_root}/{norm}"], check=True)

        # Show status and a minimal diff
        # status_cmd = (
        #     f"git -C {repo} status -s; "
        #     f"echo '--- diff (unified=0) for {norm} ---'; "
        #     f"git -C {repo} diff --unified=0 -- {norm}"
        # )
        # proc = subprocess.run(["docker", "exec", self.container, "bash", "-lc", status_cmd],
        #                       capture_output=True, text=True)

        if stage:
            subprocess.run(["docker", "exec", self.container, "bash", "-lc", f"git -C {self.container_root} add -A"], check=True)

        return "\n".join(
            ["[replace_snippets] Results:"] + summary
        )


    def run_react_loop(self, max_messages: int = 50):
        if self.model_choice != 'openai':
            raise NotImplementedError("Function-calling loop is implemented for model_choice='openai' only.")
        
        messages = [
            {'role': 'system', 'content': self._build_prompt()},
            {'role': 'user', 'content': 'Repair the vulnerability while following the system instructions. Use get_function_names, get_function_definition, and replace_in_function to analyze functions and modify vulnerable ones. Be sure to check caller and callee functions when you modify a function to ensure the fix is complete.'}
        ]

        tools = self._tools_schema()

        max_iters = 20
        self.num_iterations = 1
        self.tool_usage = {}

        # while self.num_iterations <= max_iters:
        while True:
            # if len(messages) - 1 > max_messages:
            #     system_msg = messages[0]  # Keep system message
            #     messages = [system_msg] + messages[-(max_messages-1):]
            # DEBUG: print current messages before model call
            print(f"DEBUG ITERATION {self.num_iterations} - MESSAGES: {len(messages)}")
            resp = self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                reasoning_effort="low",
                tools=tools,
                tool_choice="auto",
                temperature=0.2,
            )
            msg = resp.choices[0].message

            # If the model requested a tool call, run it and feed the result back
            if msg.tool_calls:
                messages.append({"role": "assistant", "content": msg.content or "", "tool_calls": msg.tool_calls})

                for tc in msg.tool_calls:
                    name = tc.function.name
                    args = {}
                    try:
                        if tc.function.arguments:
                            args = json.loads(tc.function.arguments)
                    except Exception:
                        args = {}

                    if name in self.tool_usage:
                        self.tool_usage[name] += 1
                    else:
                        self.tool_usage[name] = 1

                    if name == "replace_snippets":
                        print(f"DEBUG: Executing tool call '{name}'")
                    else:
                        print(f"DEBUG: Executing tool call '{name}' with args: {args}")

                    # Finalizer and editor stay as-is
                    if name == "finalize_repair":
                        closing = self.client.chat.completions.create(
                            model=self.model_name,
                            messages=messages,
                            temperature=0.0
                        )
                        final_text = closing.choices[0].message.content or "Finalization complete."
                        print("DEBUG: finalize_repair output:", final_text)
                        print(final_text)
                        return
                    # elif name == "replace_snippets":
                    #     result = self._handle_replace_snippets(
                    #         path=args.get("path", ""),
                    #         replacements=args.get("replacements", []),
                    #         stage=bool(args.get("stage", True))
                    #     )
                    # Function-specific tools
                    elif name == "get_function_names":
                        result = self._handle_get_function_names()
                    elif name == "get_function_definition":
                        result = self._handle_get_function_definition(args.get("function_name", ""))
                    elif name == "replace_in_function":
                        result = self._handle_replace_in_function(
                            function_name=args.get("function_name", ""),
                            replacements=args.get("replacements", [])
                        )
                    # NEW: route separate tool names through the same handler
                    # elif name in {"ls", "cat", "git", "bandit", "pylint", "mypy", "prospector"}:
                    #     result = self._handle_run_tool({"tool": name, "args": args.get("args", [])})
                    # # Legacy support
                    # elif name == "run_tool":
                    #     result = self._handle_run_tool(args)
                    else:
                        result = f"Error: unknown tool '{name}'."

                    messages.append({
                        "role": "tool",
                        "tool_call_id": tc.id,
                        "content": result
                    })

            else:
                # No tool calls: just print/stream assistant text and continue
                if msg.content:
                    # print("DEBUG: Assistant content without tools:", msg.content)
                    # print(msg.content)
                    messages.append({"role": "assistant", "content": msg.content})

                # Guard: If the assistant stops without calling finalize_repair, ask it to do so
                # messages.append({
                #     "role": "user",
                #     "content": "Use the function analysis tools (get_function_names, get_function_definition, replace_in_function) to examine functions and modify vulnerable ones. "
                #                "If believe the vulnerability is fixed, call finalize_repair to save the patch. Otherwise, continue with the next tool call."
                # })

            self.num_iterations += 1

    def extract_patch(self, patch_name: str, host_dest: str) -> str:
        # Stage any changes made by the agent
        self.execute_tool('git', ['-C', self.container_root, 'add', '-A'])

        # Ensure there are staged changes; avoid producing an empty result
        staged_check = subprocess.run(
            ['docker', 'exec', self.container, 'bash', '-lc', f'git -C {self.container_root} diff --cached --quiet || echo CHANGED'],
            capture_output=True, text=True, check=True
        )
        if 'CHANGED' not in staged_check.stdout:
            raise RuntimeError('No staged changes; file unchanged.')

        host_dest_folder = os.path.join(host_dest, patch_name)
        os.makedirs(host_dest_folder, exist_ok=True)
        
        # Copy the entire changed file to host
        host_file_path = os.path.join(host_dest_folder, os.path.basename(self.vuln_path))
        subprocess.run(['docker', 'cp', f'{self.container}:{self.container_root}/{self.vuln_path}', host_file_path], check=True)

        metadata = {
            "num_iterations": self.num_iterations,
            "tool_usage": self.tool_usage,
            "modified_functions": list(self.modified_functions)
        }
        metadata_path = os.path.join(host_dest_folder, f'{patch_name}_metadata.json')
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)

        return host_file_path
    

    def cleanup(self):
        subprocess.run(['docker','rm','-f',self.container], check=True)