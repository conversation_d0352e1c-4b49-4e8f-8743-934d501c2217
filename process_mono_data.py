#!/usr/bin/env python3
"""
Process MONO vulnerability data from JSONL to CSV.

This script processes vulnerability data by:
1. Loading JSONL data and filtering for vulnerable functions (target=1)
2. Filtering for commits that affect only a single file
3. Ensuring line changes fall within function boundaries
4. Handling commits with multiple functions by applying patches
5. Filtering for commits with single CVE IDs
6. Outputting processed CSV data

Usage:
    python process_mono_data.py <input_jsonl_path> <output_csv_path>
"""

import argparse
import pandas as pd
import re
import math
import ast
from typing import List, Dict, Any, Optional


def line_changes_within_function(row: pd.Series) -> bool:
    """
    Returns True if all line changes fall within the function range.
    """
    fn_start = row['function_numbers']['function_start']
    fn_end = row['function_numbers']['function_end']
    for change in row['line_numbers']:
        start = change['line_start']
        end = change['line_end']
        if start is None or end is None:
            return False
        if start < fn_start or end > fn_end:
            return False
    return True


def _parse_hunk_lines(hunk_text: str) -> List[tuple]:
    """Parse unified diff hunk into (tag, text) tuples."""
    lines = []
    for raw in hunk_text.splitlines():
        if raw.startswith('@@'):
            continue  # header
        if raw and raw[0] in (' ', '+', '-', '@'):
            tag, text = raw[0], raw[1:]
        else:
            tag, text = ' ', raw  # assume context if no prefix
        lines.append((tag, text))
    return lines


def _find_subsequence(haystack: List[str], needle: List[str]) -> Optional[int]:
    """Find needle subsequence in haystack, return start index or None."""
    if not needle:
        return None
    n = len(needle)
    for i in range(len(haystack) - n + 1):
        ok = True
        for j, s in enumerate(needle):
            if haystack[i + j].rstrip() != s.rstrip():
                ok = False
                break
        if ok:
            return i
    return None


def _apply_single_hunk(func_text: str, hunk_text: str) -> str:
    """Apply a single unified diff hunk to function text."""
    func_lines = func_text.splitlines()
    hunk = _parse_hunk_lines(hunk_text)

    src_seq = [t for tag, t in hunk if tag in (' ', '-')]
    start = _find_subsequence(func_lines, src_seq)

    if start is None:
        ctx_seq = [t for tag, t in hunk if tag == ' ']
        start = _find_subsequence(func_lines, ctx_seq)

    if start is None:
        return func_text

    i = start
    new_segment = []
    for tag, text in hunk:
        if tag == ' ':
            new_segment.append(func_lines[i])
            i += 1
        elif tag == '-':
            i += 1
        elif tag == '+':
            new_segment.append(text)

    consumed = len(src_seq)
    func_lines = func_lines[:start] + new_segment + func_lines[start + consumed:]
    return "\n".join(func_lines)


def _coerce_line_numbers(val: Any) -> List[str]:
    """
    Normalize `line_numbers` into a list of hunk strings.
    Accepts: NaN/None, dict, list/tuple, numpy array, pandas Series, or string.
    """
    # None/NaN
    if val is None or (isinstance(val, float) and math.isnan(val)):
        return []

    # pandas Series -> list
    if isinstance(val, pd.Series):
        val = val.tolist()

    # numpy array -> list (optional import)
    try:
        import numpy as np
        if isinstance(val, np.ndarray):
            val = val.tolist()
    except Exception:
        pass

    # dict -> wrap
    if isinstance(val, dict):
        val = [val]

    # string -> either parse list/dicts or single hunk string
    if isinstance(val, str):
        s = val.strip()
        if (s.startswith('[') and s.endswith(']')) or (s.startswith('{') and s.endswith('}')):
            try:
                parsed = ast.literal_eval(s)
                val = parsed if isinstance(parsed, (list, tuple)) else [parsed]
            except Exception:
                val = [s]
        else:
            val = [s]

    # ensure iterable list/tuple
    if not isinstance(val, (list, tuple)):
        val = [val]

    # Extract actual hunk strings
    hunks = []
    for item in val:
        if not item:
            continue
        if isinstance(item, dict):
            h = item.get('line_change')
            if h:
                hunks.append(h)
        else:
            hunks.append(str(item))
    return hunks


def _apply_all_hunks_to_function(func_text: str, line_numbers: Any) -> str:
    """Apply all hunks from line_numbers to function text."""
    hunks = _coerce_line_numbers(line_numbers)
    if not hunks:
        return func_text
    out = func_text
    for hunk_text in hunks:
        out = _apply_single_hunk(out, hunk_text)
    return out


def process_mono_data(input_path: str, output_path: str) -> None:
    """
    Process MONO vulnerability data from JSONL to CSV.
    
    Args:
        input_path: Path to input JSONL file
        output_path: Path to output CSV file
    """
    print(f"Loading data from {input_path}...")
    
    # Load JSONL data
    df = pd.read_json(input_path, lines=True)
    print(f"Loaded {len(df)} total records")
    
    # Filter for vulnerable functions only (target=1)
    df = df[df["target"] == 1].copy()
    print(f"After filtering for vulnerable functions: {len(df)} records")
    
    # Filter for commits where all functions are in the same file
    file_counts = (
        df.groupby("commit_id")["file_name"]
        .nunique()
        .reset_index()
        .rename(columns={"file_name": "num_unique_files"})
    )
    
    single_file_commits = file_counts[file_counts["num_unique_files"] == 1]["commit_id"]
    df = df[df["commit_id"].isin(single_file_commits)].copy().reset_index(drop=True)
    print(f"After filtering for single-file commits: {len(df)} records")
    
    # Filter for line changes within function boundaries
    df = df[df.apply(line_changes_within_function, axis=1)].copy().reset_index(drop=True)
    print(f"After filtering for changes within function boundaries: {len(df)} records")
    
    # Get commits with multiple rows (multiple functions)
    commit_counts = df['commit_id'].value_counts()
    multi_row_commit_ids = commit_counts[commit_counts > 1].index
    multi_row_df = df[df['commit_id'].isin(multi_row_commit_ids)].copy().reset_index(drop=True)
    
    if len(multi_row_df) > 0:
        print(f"Processing {len(multi_row_df)} records from {len(multi_row_commit_ids)} multi-function commits...")
        
        # Apply patches to create function_after
        multi_row_df['function_after'] = multi_row_df.apply(
            lambda row: _apply_all_hunks_to_function(row['function'], row['line_numbers']),
            axis=1
        )
        
        # Filter for commits with single CVE ID
        commit_cve_counts = multi_row_df.groupby('commit_id')['cve_id'].nunique()
        single_cve_commits = commit_cve_counts[commit_cve_counts == 1].index
        multi_row_df = multi_row_df[multi_row_df['commit_id'].isin(single_cve_commits)].copy().reset_index(drop=True)
        
        print(f"After filtering for single CVE per commit: {len(multi_row_df)} records")
        print(f"Final dataset covers {multi_row_df['commit_id'].nunique()} commits and {multi_row_df['cve_id'].nunique()} CVEs")
        
        # Save to CSV
        multi_row_df.to_csv(output_path, index=False)
        print(f"Saved processed data to {output_path}")
    else:
        print("No multi-function commits found. Creating empty output file.")
        # Create empty DataFrame with expected columns and save
        empty_df = pd.DataFrame(columns=df.columns.tolist() + ['function_after'])
        empty_df.to_csv(output_path, index=False)
        print(f"Saved empty dataset to {output_path}")


def main():
    parser = argparse.ArgumentParser(
        description="Process MONO vulnerability data from JSONL to CSV",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    parser.add_argument("input_path", help="Path to input JSONL file")
    parser.add_argument("output_path", help="Path to output CSV file")
    
    args = parser.parse_args()
    
    try:
        process_mono_data(args.input_path, args.output_path)
    except Exception as e:
        print(f"Error processing data: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
